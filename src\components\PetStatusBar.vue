<script setup>
import { computed } from 'vue';

const props = defineProps({
  happiness: Number,
  hunger: Number,
  energy: Number,
  cleanliness: Number,
  health: Number
});

const happinessPercent = computed(() => props.happiness || 0);
const hungerPercent = computed(() => props.hunger || 0);
const energyPercent = computed(() => props.energy || 0);
const cleanlinessPercent = computed(() => props.cleanliness || 0);
const healthPercent = computed(() => props.health || 0);

// Get status color based on value
function getStatusColor(value) {
  if (value > 70) return 'var(--success-color)';
  if (value > 40) return 'var(--warning-color)';
  return 'var(--error-color)';
}

// For hunger, lower is better
function getHungerColor(value) {
  if (value < 30) return 'var(--success-color)';
  if (value < 60) return 'var(--warning-color)';
  return 'var(--error-color)';
}

// Computed colors
const happinessColor = computed(() => getStatusColor(props.happiness));
const hungerColor = computed(() => getHungerColor(props.hunger));
const energyColor = computed(() => getStatusColor(props.energy));
const cleanlinessColor = computed(() => getStatusColor(props.cleanliness));
const healthColor = computed(() => getStatusColor(props.health));
</script>

<template>
  <div class="status-bars">
    <div class="status-item">
      <div class="status-label">Felicidad</div>
      <div class="status-bar-container">
        <div class="status-bar" 
             :style="{ width: `${happiness}%`, backgroundColor: happinessColor }"></div>
      </div>
      <div class="status-value">{{ happiness }}%</div>
    </div>
    
    <div class="status-item">
      <div class="status-label">Hambre</div>
      <div class="status-bar-container">
        <div class="status-bar" 
             :style="{ width: `${hunger}%`, backgroundColor: hungerColor }"></div>
      </div>
      <div class="status-value">{{ hunger }}%</div>
    </div>
    
    <div class="status-item">
      <div class="status-label">Energía</div>
      <div class="status-bar-container">
        <div class="status-bar" 
             :style="{ width: `${energy}%`, backgroundColor: energyColor }"></div>
      </div>
      <div class="status-value">{{ energy }}%</div>
    </div>
    
    <div class="status-item">
      <div class="status-label">Limpieza</div>
      <div class="status-bar-container">
        <div class="status-bar" 
             :style="{ width: `${cleanliness}%`, backgroundColor: cleanlinessColor }"></div>
      </div>
      <div class="status-value">{{ cleanliness }}%</div>
    </div>
    
    <div class="status-item">
      <div class="status-label">Salud</div>
      <div class="status-bar-container">
        <div class="status-bar" 
             :style="{ width: `${health}%`, backgroundColor: healthColor }"></div>
      </div>
      <div class="status-value">{{ health }}%</div>
    </div>
  </div>
</template>

<style scoped>
.status-bars {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.status-label {
  width: 80px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--neutral-700);
}

.status-bar-container {
  flex-grow: 1;
  height: 12px;
  background-color: var(--neutral-300);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.status-bar {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--transition-normal), background-color var(--transition-normal);
}

.status-value {
  width: 40px;
  text-align: right;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

@media (max-width: 768px) {
  .status-item {
    flex-wrap: wrap;
  }
  
  .status-label {
    width: auto;
    min-width: 80px;
  }
  
  .status-bar-container {
    flex-basis: 100%;
    order: 3;
    margin-top: var(--spacing-xs);
  }
}
</style>