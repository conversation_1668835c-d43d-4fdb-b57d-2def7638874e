<script setup>
import { computed } from 'vue';
import { PetType } from '../stores/petTypes.js';

const props = defineProps({
  mood: String,
  petType: String
});

// Get appropriate image URL based on pet type and mood
const petImageUrl = computed(() => {
  const type = props.petType || PetType.CAT;
  
  // Using Pexels stock photos
  switch (type) {
    case PetType.CAT:
      if (props.mood === 'happy') return 'https://images.pexels.com/photos/2061057/pexels-photo-2061057.jpeg';
      if (props.mood === 'hungry') return 'https://images.pexels.com/photos/1056251/pexels-photo-1056251.jpeg';
      if (props.mood === 'sleepy') return 'https://images.pexels.com/photos/416160/pexels-photo-416160.jpeg';
      if (props.mood === 'dirty') return 'https://images.pexels.com/photos/1317844/pexels-photo-1317844.jpeg';
      if (props.mood === 'sad') return 'https://images.pexels.com/photos/617278/pexels-photo-617278.jpeg';
      return 'https://images.pexels.com/photos/45201/kitty-cat-kitten-pet-45201.jpeg';
      
    case PetType.DOG:
      if (props.mood === 'happy') return 'https://images.pexels.com/photos/551628/pexels-photo-551628.jpeg';
      if (props.mood === 'hungry') return 'https://images.pexels.com/photos/1916362/pexels-photo-1916362.jpeg';
      if (props.mood === 'sleepy') return 'https://images.pexels.com/photos/2737393/pexels-photo-2737393.jpeg';
      if (props.mood === 'dirty') return 'https://images.pexels.com/photos/825949/pexels-photo-825949.jpeg';
      if (props.mood === 'sad') return 'https://images.pexels.com/photos/2785431/pexels-photo-2785431.jpeg';
      return 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg';
      
    case PetType.BUNNY:
      if (props.mood === 'happy') return 'https://images.pexels.com/photos/4001296/pexels-photo-4001296.jpeg';
      if (props.mood === 'hungry') return 'https://images.pexels.com/photos/5179900/pexels-photo-5179900.jpeg';
      if (props.mood === 'sleepy') return 'https://images.pexels.com/photos/6846089/pexels-photo-6846089.jpeg';
      if (props.mood === 'dirty') return 'https://images.pexels.com/photos/6440430/pexels-photo-6440430.jpeg';
      if (props.mood === 'sad') return 'https://images.pexels.com/photos/4282425/pexels-photo-4282425.jpeg';
      return 'https://images.pexels.com/photos/326012/pexels-photo-326012.jpeg';
      
    default:
      return 'https://images.pexels.com/photos/45201/kitty-cat-kitten-pet-45201.jpeg';
  }
});

// Get mood text
const moodText = computed(() => {
  switch (props.mood) {
    case 'happy': return '¡Feliz!';
    case 'hungry': return 'Hambriento';
    case 'sleepy': return 'Cansado';
    case 'dirty': return 'Necesita limpieza';
    case 'sad': return 'Triste';
    default: return 'Neutral';
  }
});

// Get the background color based on mood
const moodBackground = computed(() => {
  switch (props.mood) {
    case 'happy': return 'var(--accent-light)';
    case 'hungry': return 'var(--warning-color)';
    case 'sleepy': return 'var(--neutral-300)';
    case 'dirty': return 'var(--neutral-400)';
    case 'sad': return 'var(--secondary-light)';
    default: return 'var(--neutral-200)';
  }
});
</script>

<template>
  <div class="pet-display" :style="{ backgroundColor: moodBackground }">
    <div class="pet-image">
      <img :src="petImageUrl" :alt="petType" />
    </div>
    <div class="mood-indicator">
      <span>Estado: {{ moodText }}</span>
    </div>
  </div>
</template>

<style scoped>
.pet-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  transition: background-color var(--transition-slow);
}

.pet-image {
  width: 250px;
  height: 250px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 4px solid white;
  transition: transform var(--transition-normal);
}

.pet-image:hover {
  transform: scale(1.02);
}

.pet-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all var(--transition-normal);
}

.mood-indicator {
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: white;
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  box-shadow: var(--shadow-sm);
}

@media (max-width: 768px) {
  .pet-image {
    width: 200px;
    height: 200px;
  }
}
</style>