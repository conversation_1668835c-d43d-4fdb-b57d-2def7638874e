<script setup>
import { defineEmits } from 'vue';

const emits = defineEmits(['action']);

function handleAction(type) {
  emits('action', type);
}
</script>

<template>
  <div class="action-menu">
    <h3>Acciones</h3>
    
    <div class="action-buttons">
      <button class="action-btn feed-btn" @click="handleAction('feed')">
        <span class="action-icon">🍔</span>
        <span class="action-label">Alimentar</span>
      </button>
      
      <button class="action-btn play-btn" @click="handleAction('play')">
        <span class="action-icon">🎮</span>
        <span class="action-label">Jugar</span>
      </button>
      
      <button class="action-btn rest-btn" @click="handleAction('rest')">
        <span class="action-icon">😴</span>
        <span class="action-label">Descansar</span>
      </button>
      
      <button class="action-btn clean-btn" @click="handleAction('clean')">
        <span class="action-icon">🧼</span>
        <span class="action-label">Limpiar</span>
      </button>
    </div>
  </div>
</template>

<style scoped>
.action-menu {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

h3 {
  margin-bottom: var(--spacing-md);
  color: var(--neutral-800);
  text-align: center;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
  background-color: var(--neutral-200);
  color: var(--neutral-800);
}

.action-btn:hover {
  transform: translateY(-3px);
}

.action-icon {
  font-size: var(--font-size-xl);
}

.action-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.feed-btn:hover {
  background-color: #FFE0B2;
}

.play-btn:hover {
  background-color: #BBDEFB;
}

.rest-btn:hover {
  background-color: #D1C4E9;
}

.clean-btn:hover {
  background-color: #B2DFDB;
}

@media (max-width: 768px) {
  .action-buttons {
    grid-template-columns: 1fr 1fr;
  }
}
</style>