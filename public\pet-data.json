{"supabase": {"tables": [{"name": "users", "columns": [{"name": "id", "type": "uuid", "primaryKey": true, "description": "User ID from auth.users"}, {"name": "email", "type": "text", "description": "User email"}, {"name": "username", "type": "text", "description": "User display name"}, {"name": "created_at", "type": "timestamp with time zone", "description": "When the user was created"}], "rls_policies": [{"name": "Users can read their own data", "operation": "SELECT", "definition": "auth.uid() = id"}, {"name": "Users can update their own data", "operation": "UPDATE", "definition": "auth.uid() = id"}]}, {"name": "pets", "columns": [{"name": "id", "type": "uuid", "primaryKey": true, "default": "uuid_generate_v4()"}, {"name": "owner_id", "type": "uuid", "references": "users.id", "description": "Reference to the pet owner"}, {"name": "name", "type": "text", "description": "Pet name"}, {"name": "type", "type": "text", "description": "Pet type (cat, dog, bunny)"}, {"name": "happiness", "type": "integer", "default": 50, "description": "Pet happiness level (0-100)"}, {"name": "hunger", "type": "integer", "default": 20, "description": "Pet hunger level (0-100)"}, {"name": "energy", "type": "integer", "default": 80, "description": "Pet energy level (0-100)"}, {"name": "cleanliness", "type": "integer", "default": 80, "description": "Pet cleanliness level (0-100)"}, {"name": "health", "type": "integer", "default": 100, "description": "Pet health level (0-100)"}, {"name": "last_interaction", "type": "timestamp with time zone", "description": "When the pet was last interacted with"}, {"name": "created_at", "type": "timestamp with time zone", "default": "now()", "description": "When the pet was created"}], "rls_policies": [{"name": "Users can read their own pets", "operation": "SELECT", "definition": "auth.uid() = owner_id"}, {"name": "Users can update their own pets", "operation": "UPDATE", "definition": "auth.uid() = owner_id"}, {"name": "Users can insert their own pets", "operation": "INSERT", "definition": "auth.uid() = owner_id"}]}, {"name": "messages", "columns": [{"name": "id", "type": "uuid", "primaryKey": true, "default": "uuid_generate_v4()"}, {"name": "pet_id", "type": "uuid", "references": "pets.id", "description": "Reference to the pet"}, {"name": "content", "type": "text", "description": "Message content"}, {"name": "sender", "type": "text", "description": "Message sender (user or pet)"}, {"name": "timestamp", "type": "timestamp with time zone", "default": "now()", "description": "When the message was sent"}], "rls_policies": [{"name": "Users can read their pets' messages", "operation": "SELECT", "definition": "auth.uid() IN (SELECT owner_id FROM pets WHERE id = pet_id)"}, {"name": "Users can insert messages for their pets", "operation": "INSERT", "definition": "auth.uid() IN (SELECT owner_id FROM pets WHERE id = pet_id)"}]}, {"name": "interactions", "columns": [{"name": "id", "type": "uuid", "primaryKey": true, "default": "uuid_generate_v4()"}, {"name": "pet_id", "type": "uuid", "references": "pets.id", "description": "Reference to the pet"}, {"name": "type", "type": "text", "description": "Interaction type (feed, play, clean, rest, etc.)"}, {"name": "description", "type": "text", "description": "Description of the interaction"}, {"name": "timestamp", "type": "timestamp with time zone", "default": "now()", "description": "When the interaction occurred"}], "rls_policies": [{"name": "Users can read their pets' interactions", "operation": "SELECT", "definition": "auth.uid() IN (SELECT owner_id FROM pets WHERE id = pet_id)"}, {"name": "Users can insert interactions for their pets", "operation": "INSERT", "definition": "auth.uid() IN (SELECT owner_id FROM pets WHERE id = pet_id)"}]}]}}