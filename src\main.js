import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createRouter, createWebHistory } from 'vue-router';

import App from './App.vue';
import Home from './views/Home.vue';
import Login from './views/Login.vue';
import Register from './views/Register.vue';
import PetInteraction from './views/PetInteraction.vue';
import CreatePet from './views/CreatePet.vue';
import MisMascotas from './views/MisMascotas.vue';
import { useAuthStore } from './stores/authStore';

import './style.css';

const routes = [
  { path: '/', component: Home },  
  { path: '/login', component: Login, meta: { guest: true } },
  { path: '/register', component: Register, meta: { guest: true } },
  { path: '/pet/:id?', name: 'PetInteraction', component: PetInteraction, meta: { requiresAuth: true } },
  { path: '/mis-mascotas', name: 'MisMascotas', component: MisMascotas, meta: { requiresAuth: true } },
  { path: '/create-pet', component: CreatePet, meta: { requiresAuth: true } },
  { path: '/profile', component: () => import('./views/Profile.vue'), meta: { requiresAuth: true } }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();
  const isLoggedIn = !!authStore.user;
  if (to.meta.requiresAuth && !isLoggedIn) {
    next('/login');
  } else if (to.meta.guest && isLoggedIn) {
    next('/');
  } else {
    next();
  }
});

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(router);

app.mount('#app');
