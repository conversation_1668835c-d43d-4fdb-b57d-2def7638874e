<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/authStore.js';

const router = useRouter();
const authStore = useAuthStore();

const email = ref('');
const password = ref('');
const errorMessage = ref('');
const isLoading = ref(false);

async function handleLogin() {
  if (!email.value || !password.value) {
    errorMessage.value = 'Por favor completa todos los campos';
    return;
  }
  
  isLoading.value = true;
  errorMessage.value = '';
  
  try {
    await authStore.login(email.value, password.value);
    
    if (authStore.error) {
      errorMessage.value = authStore.error;
    } else {
      router.push('/');
    }
  } catch (error) {
    errorMessage.value = error.message || 'Error al iniciar sesión';
  } finally {
    isLoading.value = false;
  }
}

function goToRegister() {
  router.push('/register');
}
</script>

<template>
  <div class="auth-container">
    <div class="auth-card">
      <h1 class="auth-title">Iniciar <PERSON></h1>
      
      <form @submit.prevent="handleLogin" class="auth-form">
        <div class="form-group">
          <label for="email">Email</label>
          <input 
            id="email" 
            type="email" 
            v-model="email"
            placeholder="<EMAIL>"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="password">Contraseña</label>
          <input 
            id="password" 
            type="password" 
            v-model="password"
            placeholder="Tu contraseña"
            required
          />
        </div>
        
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
        
        <button 
          type="submit" 
          class="btn-primary login-btn"
          :disabled="isLoading"
        >
          <span v-if="isLoading">Cargando...</span>
          <span v-else>Iniciar Sesión</span>
        </button>
      </form>
      
      <div class="auth-footer">
        <p>¿No tienes cuenta?</p>
        <button class="btn-link" @click="goToRegister">Regístrate</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--primary-light);
  padding: var(--spacing-md);
}

.auth-card {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 400px;
}

.auth-title {
  text-align: center;
  color: var(--primary-dark);
  margin-bottom: var(--spacing-xl);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-group label {
  font-weight: var(--font-weight-medium);
  color: var(--neutral-700);
}

.login-btn {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
}

.login-btn:disabled {
  background-color: var(--neutral-400);
  cursor: not-allowed;
}

.error-message {
  color: var(--error-color);
  font-size: var(--font-size-sm);
  padding: var(--spacing-xs) 0;
}

.auth-footer {
  margin-top: var(--spacing-xl);
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.btn-link {
  color: var(--primary-color);
  background: none;
  border: none;
  padding: 0;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
}

.btn-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}
</style>