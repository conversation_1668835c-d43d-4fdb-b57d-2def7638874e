import { defineStore } from 'pinia'
import { ref } from 'vue'
import { supabase } from '../services/supabase'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const loading = ref(false)
  const error = ref(null)

  async function initialize() {
    const { data } = await supabase.auth.getSession()
    if (data.session) {
      user.value = {
        id: data.session.user.id,
        email: data.session.user.email || '',
      }
    } else {
      user.value = null;
    }
    // Listen for auth state changes (login, logout, refresh)
    supabase.auth.onAuthStateChange((event, session) => {
      if (session && session.user) {
        user.value = {
          id: session.user.id,
          email: session.user.email || '',
        }
      } else {
        user.value = null;
      }
    });
  }

  async function login(email, password) {
    try {
      loading.value = true
      error.value = null
      const { data, error: err } = await supabase.auth.signInWithPassword({ email, password })
      if (err) throw err
      if (data.user) {
        user.value = {
          id: data.user.id,
          email: data.user.email || '',
        }
      }
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  async function register(email, password, username) {
    try {
      loading.value = true
      error.value = null
      const { data, error: err } = await supabase.auth.signUp({
        email,
        password,
        options: { data: { username } }
      })
      if (err) throw err
      if (data.user) {
        user.value = {
          id: data.user.id,
          email: data.user.email || '',
          username
        }
        // Insertar usuario en la tabla users
        await supabase.from('users').insert({
          id: data.user.id,
          email: data.user.email,
          username
        })
      }
    } catch (err) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  async function logout() {
    try {
      await supabase.auth.signOut()
      user.value = null
    } catch (err) {
      error.value = err.message
    }
  }

  return {
    user,
    loading,
    error,
    initialize,
    login,
    register,
    logout
  }
})
