<template>
  <div class="app-container">
    <nav class="main-nav">
      <router-link to="/">Inicio</router-link>
      <router-link v-if="authStore.user" to="/mis-mascotas">Mis Mascotas</router-link>
      <router-link v-if="authStore.user" to="/create-pet"><PERSON><PERSON><PERSON></router-link>
      <router-link v-if="authStore.user" to="/profile">Perfil</router-link>
      <router-link v-if="!authStore.user" to="/login">Iniciar sesión</router-link>
      <button v-if="authStore.user" class="logout-btn" @click="logout">Cerrar sesión</button>
    </nav>
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from './stores/authStore';

const authStore = useAuthStore();
const router = useRouter();

function logout() {
  authStore.logout();
  router.push('/');
}

onMounted(async () => {
  await authStore.initialize();
});
</script>

<style>
:root {
  /* Color palette */
  --primary-color: #58B2DC; /* Primary blue */
  --primary-light: #A5D7F0;
  --primary-dark: #1A6C99;
  
  --secondary-color: #F2B8C6; /* Secondary pink */
  --secondary-light: #FFD8E1;
  --secondary-dark: #D87E96;
  
  --accent-color: #5EB28E; /* Accent green */
  --accent-light: #A0D9C4;
  --accent-dark: #318463;
  
  --neutral-100: #FFFFFF;
  --neutral-200: #F5F5F5;
  --neutral-300: #E0E0E0;
  --neutral-400: #BDBDBD;
  --neutral-500: #9E9E9E;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;
  
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  
  /* Spacing system (8px base) */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* Border radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --radius-full: 9999px;
  
  /* Font weights */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  /* Font sizes */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-xxl: 2rem;
  
  /* Line heights */
  --line-height-body: 1.5;
  --line-height-heading: 1.2;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Animation timing */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
}

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
 
  width: 100%;
  font-family: 'Inter', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: var(--line-height-body);
  color: var(--neutral-900);
  background-color: var(--neutral-100);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

h1, h2, h3, h4, h5, h6 {
  line-height: var(--line-height-heading);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--font-size-xxl);
}

h2 {
  font-size: var(--font-size-xl);
}

h3 {
  font-size: var(--font-size-lg);
}

p {
  margin-bottom: var(--spacing-md);
}

button {
  cursor: pointer;
  font-family: inherit;
  border: none;
  background: none;
  font-size: var(--font-size-md);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: all var(--transition-normal);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--neutral-800);
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
}

.btn-accent {
  background-color: var(--accent-color);
  color: white;
}

.btn-accent:hover {
  background-color: var(--accent-dark);
}

input, textarea {
  font-family: inherit;
  font-size: var(--font-size-md);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  width: 100%;
  transition: border-color var(--transition-normal);
}

input:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Utility classes */
.text-center {
  text-align: center;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Navigation styles */
.main-nav {
  display: flex;
  gap: 1rem;
  align-items: center;
  background: var(--primary-light);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}
.main-nav a {
  color: var(--primary-dark);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: background var(--transition-normal);
}
.main-nav a.router-link-exact-active {
  background: var(--primary-color);
  color: white;
}
.logout-btn {
  background: var(--error-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
}
.logout-btn:hover {
  background: var(--primary-dark);
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  :root {
    --font-size-xxl: 1.75rem;
    --font-size-xl: 1.25rem;
    --spacing-xxl: 32px;
  }
  
  .container {
    padding: 0 var(--spacing-sm);
  }
}
</style>