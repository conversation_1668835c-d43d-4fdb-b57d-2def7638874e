# Mascota Virtual Interactiva

Una aplicación web que simula una mascota virtual con la que los usuarios pueden interactuar. La mascota responde a las acciones del usuario, muestra emociones y requiere cuidados básicos.

## Características

- **Interacción Básica**: El usuario puede enviar mensajes a la mascota y recibir respuestas generadas por IA.
- **Visualización de la Mascota**: Representación gráfica simple que cambia según el estado emocional.
- **Autenticación de Usuarios**: Registro e inicio de sesión para personalizar la experiencia.
- **Persistencia de Datos**: Información del usuario y estado de la mascota guardados en Supabase.
- **Sistema de Estados**: Felicidad, energía, hambre, salud, y limpieza.
- **Mecánicas de Interacción**: Alimentación, juego, descanso y limpieza.

## Tecnologías Utilizadas

### Frontend
- Vue 3 con Composition API
- Vite
- TypeScript
- CSS (Flexbox/Grid)

### Backend y Base de Datos
- Supabase:
  - Base de datos PostgreSQL
  - Autenticación de usuarios
  - Políticas RLS para seguridad

## Configuración del Proyecto

### Requisitos Previos
- Node.js y npm
- Cuenta en Supabase

### Instalación

1. Clonar el repositorio
```
git clone [URL_DEL_REPOSITORIO]
cd mascota-virtual
```

2. Instalar dependencias
```
npm install
```

3. Configurar variables de entorno
Crea un archivo `.env` en la raíz del proyecto:
```
VITE_SUPABASE_URL=tu_url_de_supabase
VITE_SUPABASE_ANON_KEY=tu_clave_anonima_de_supabase
```

4. Configurar Supabase
- Crea un nuevo proyecto en Supabase
- Configura las tablas según el esquema en `public/pet-data.json`
- Configura la autenticación con Email/Password

5. Iniciar el servidor de desarrollo
```
npm run dev
```

## Estructura de la Base de Datos

### Tablas
- **users**: Información de los usuarios
- **pets**: Datos de las mascotas virtuales
- **messages**: Mensajes entre usuario y mascota
- **interactions**: Registro de interacciones con la mascota

### Esquema
El esquema completo de la base de datos se encuentra en `public/pet-data.json`

## API

### Endpoints de Supabase
La aplicación utiliza la API de Supabase para todas las operaciones CRUD.

## Funcionalidades Core

### Sistema de Estados de la Mascota
- **Felicidad (0-100%)**: Afectada por juego e interacciones
- **Energía (0-100%)**: Afectada por descanso, juego y alimentación
- **Hambre (0-100%)**: Afectada por alimentación y otras actividades
- **Limpieza (0-100%)**: Afectada por la limpieza
- **Salud (0-100%)**: Depende del balance de los otros estados

### Mecánicas de Interacción
- **Alimentación**: +20 hambre, -5 energía
- **Juego**: +15 felicidad, -10 energía, +10 hambre
- **Descanso**: +30 energía, +5 hambre
- **Limpieza**: +30 limpieza, +5 felicidad

### Sistema de Persistencia
- Autoguardado cada 5 minutos
- Actualización en tiempo real de estados
- Historial de interacciones

## Licencia
[MIT](LICENSE)