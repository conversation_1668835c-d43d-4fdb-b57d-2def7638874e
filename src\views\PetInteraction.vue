<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/authStore.js';
import { usePetStore } from '../stores/petStore.js';
import PetDisplay from '../components/PetDisplay.vue';
import PetStatusBar from '../components/PetStatusBar.vue';
import PetActionMenu from '../components/PetActionMenu.vue';
import ChatInterface from '../components/ChatInterface.vue';

const router = useRouter();
const authStore = useAuthStore();
const petStore = usePetStore();
const route = useRoute();

const loading = ref(true);
const showModal = ref(false);
const modalType = ref('');

// Direct access to store messages - keep it simple
const messages = computed(() => {
  const storeMessages = petStore.messages || [];
  console.log('PetInteraction computed: Store has', storeMessages.length, 'messages');
  return storeMessages;
});

// Check if user is logged in
onMounted(async () => {
  if (!authStore.user) {
    router.push('/login');
    return;
  }

  try {
    const petId = route.params.id;
    let pet;

    if (petId) {
      // Find pet by id from store or fetch
      await petStore.fetchPets(authStore.user.id);
      pet = petStore.pets.find(p => p.id == petId);
      if (pet) {
        console.log('Setting current pet:', pet.id);
        await petStore.setCurrentPet(pet);
        console.log('After setCurrentPet - store messages:', petStore.messages?.length || 0);
      } else {
        // fallback: fetch from DB if not in store
        console.warn('Pet not found with ID:', petId);
        router.push('/mis-mascotas');
        return;
      }
    } else {
      pet = await petStore.getPetByOwnerId(authStore.user.id);
    }

    if (!pet && !petStore.pet) {
      router.push('/create-pet');
    } else {
      loading.value = false;
    }
  } catch (error) {
    console.error('Error in PetInteraction onMounted:', error);
    loading.value = false;
  }
});

// Make debug functions available globally for testing
if (typeof window !== 'undefined') {
  window.debugPetStore = {
    testSupabase: petStore.testSupabaseConnection,
    loadMessages: petStore.loadMessages,
    store: petStore,
    messages: messages
  };
}

// Clean up when component unmounts
onUnmounted(() => {
  petStore.cleanup();
});

function logout() {
  petStore.cleanup();
  authStore.logout();
  router.push('/login');
}

function handlePetAction(action) {
  switch (action) {
    case 'feed':
      petStore.feedPet();
      break;
    case 'play':
      petStore.playWithPet();
      break;
    case 'rest':
      petStore.restPet();
      break;
    case 'clean':
      petStore.cleanPet();
      break;
    default:
      break;
  }
}

function openHelp() {
  modalType.value = 'help';
  showModal.value = true;
}

function closeModal() {
  showModal.value = false;
}

// Save pet state every 5 minutes and when pet stats change
watch(() => petStore.pet, () => {
  if (petStore.pet) {
    const now = new Date();
    const lastSave = petStore.lastSave;
    
    // If it's been more than 5 minutes since last save, save the pet state
    if (!lastSave || now.getTime() - lastSave.getTime() > 5 * 60 * 1000) {
      petStore.savePetState();
    }
  }
}, { deep: true });
</script>

<template>
  <div class="pet-page">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Cargando mascota...</p>
    </div>
    
    <template v-else>
      <header class="page-header">
        <h1 v-if="petStore.pet">{{ petStore.pet.name }}</h1>
        <div class="header-actions">
          <button class="btn-help" @click="openHelp">?</button>
        </div>
      </header>
      
      <main class="pet-container">
        <!-- Pet visualization -->
        <PetDisplay :mood="petStore.mood" :pet-type="petStore.pet?.type" />
        
        <!-- Status indicators -->
        <PetStatusBar 
          :happiness="petStore.pet?.happiness || 0"
          :hunger="petStore.pet?.hunger || 0"
          :energy="petStore.pet?.energy || 0"
          :cleanliness="petStore.pet?.cleanliness || 0"
          :health="petStore.pet?.health || 0"
        />
        
        <div class="interaction-area">
          <!-- Action menu -->
          <PetActionMenu @action="handlePetAction" />
          
          <!-- Chat interface -->
          <ChatInterface
            :messages="messages"
            @send-message="petStore.sendMessage"
          />
        </div>
      </main>
      
      <!-- Help modal -->
      <div v-if="showModal" class="modal-overlay" @click.self="closeModal">
        <div class="modal-content">
          <button class="modal-close" @click="closeModal">×</button>
          
          <div v-if="modalType === 'help'">
            <h2>Ayuda - Cómo cuidar a tu mascota</h2>
            
            <div class="help-content">
              <h3>Estados de la mascota</h3>
              <p>Tu mascota tiene varios estados que debes monitorear:</p>
              <ul>
                <li><strong>Felicidad:</strong> Aumenta jugando con tu mascota</li>
                <li><strong>Hambre:</strong> Disminuye alimentando a tu mascota</li>
                <li><strong>Energía:</strong> Aumenta cuando tu mascota descansa</li>
                <li><strong>Limpieza:</strong> Aumenta limpiando a tu mascota</li>
                <li><strong>Salud:</strong> Se mantiene cuidando los otros estados</li>
              </ul>
              
              <h3>Acciones</h3>
              <ul>
                <li><strong>Alimentar:</strong> -20 hambre, -5 energía</li>
                <li><strong>Jugar:</strong> +15 felicidad, -10 energía, +10 hambre</li>
                <li><strong>Descansar:</strong> +30 energía, +5 hambre</li>
                <li><strong>Limpiar:</strong> +30 limpieza, +5 felicidad</li>
              </ul>
              
              <h3>Estados de ánimo</h3>
              <p>El estado de ánimo de tu mascota depende de sus estados:</p>
              <ul>
                <li><strong>Feliz:</strong> Alta felicidad</li>
                <li><strong>Hambriento:</strong> Alta hambre</li>
                <li><strong>Cansado:</strong> Baja energía</li>
                <li><strong>Sucio:</strong> Baja limpieza</li>
                <li><strong>Triste:</strong> Baja felicidad</li>
              </ul>
              
              <h3>Chat</h3>
              <p>Puedes hablar con tu mascota y responderá según su estado de ánimo.</p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped>
.pet-page {
  min-height: 100vh;
  background-color: var(--neutral-200);
  padding-bottom: var(--spacing-xl);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: var(--spacing-md);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--neutral-300);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-xl);
  background-color: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-md);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

.btn-help {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: white;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
}

.pet-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

.interaction-area {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  font-size: var(--font-size-xl);
  background: none;
  border: none;
  cursor: pointer;
  color: var(--neutral-600);
}

.help-content {
  margin-top: var(--spacing-lg);
}

.help-content h3 {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--primary-dark);
}

.help-content ul {
  padding-left: var(--spacing-lg);
}

@media (max-width: 768px) {
  .interaction-area {
    grid-template-columns: 1fr;
  }
}
</style>