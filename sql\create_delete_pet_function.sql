-- Eliminar todas las versiones posibles de la función
DROP FUNCTION IF EXISTS delete_pet_with_related_data(uuid);
DROP FUNCTION IF EXISTS delete_pet_with_related_data(pet_id uuid);
DROP FUNCTION IF EXISTS delete_pet_with_related_data(pet_id_param uuid);

-- Crear la función corregida para borrar una mascota y todos sus datos relacionados
CREATE OR REPLACE FUNCTION delete_pet_with_related_data(target_pet_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Borrar interacciones relacionadas (usando alias para evitar ambigüedad)
  DELETE FROM interactions i WHERE i.pet_id = target_pet_id;

  -- Borrar mensajes relacionados (usando alias para evitar ambigüedad)
  DELETE FROM messages m WHERE m.pet_id = target_pet_id;

  -- Borrar la mascota
  DELETE FROM pets p WHERE p.id = target_pet_id;

  -- Verificar que la mascota fue borrada
  IF NOT FOUND THEN
    RAISE NOTICE 'No se encontró la mascota con ID: %', target_pet_id;
  END IF;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    -- Log del error para debugging
    RAISE NOTICE 'Error al borrar mascota %: %', target_pet_id, SQLERRM;
    RETURN FALSE;
END;
$$;

-- Crear una función wrapper con el nombre de parámetro que espera el código
CREATE OR REPLACE FUNCTION delete_pet_with_related_data_wrapper(pet_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Borrar interacciones relacionadas (usando alias para evitar ambigüedad)
  DELETE FROM interactions i WHERE i.pet_id = pet_id_param;

  -- Borrar mensajes relacionados (usando alias para evitar ambigüedad)
  DELETE FROM messages m WHERE m.pet_id = pet_id_param;

  -- Borrar la mascota
  DELETE FROM pets p WHERE p.id = pet_id_param;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    -- Log del error para debugging
    RAISE NOTICE 'Error al borrar mascota %: %', pet_id_param, SQLERRM;
    RETURN FALSE;
END;
$$;